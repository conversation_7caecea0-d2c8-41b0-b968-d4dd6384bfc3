import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/gradient_button.dart';
import '../../widgets/common/loading_widget.dart';
import 'consultations_screen.dart';
import 'nearby_centers_screen.dart';
import 'medical_tips_screen.dart';
import 'medical_store_screen.dart';
import 'notifications_screen.dart';

class PatientDashboard extends StatefulWidget {
  const PatientDashboard({Key? key}) : super(key: key);

  @override
  State<PatientDashboard> createState() => _PatientDashboardState();
}

class _PatientDashboardState extends State<PatientDashboard> {
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final patient = authProvider.patient;
        final user = authProvider.user;

        if (patient == null || user == null) {
          return const Scaffold(
            body: Center(
              child: PatientLoadingWidget(message: 'جاري تحميل البيانات...'),
            ),
          );
        }

        return Scaffold(
          body: CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                expandedHeight: 280,
                floating: false,
                pinned: true,
                backgroundColor: AppColors.patientPrimary,
                actions: [
                  IconButton(
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const NotificationsScreen(),
                        ),
                      );
                    },
                    icon: Stack(
                      children: [
                        const Icon(
                          Icons.notifications,
                          color: AppColors.white,
                          size: 24,
                        ),
                        // Notification badge
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: AppColors.error,
                              shape: BoxShape.circle,
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 16,
                              minHeight: 16,
                            ),
                            child: const Text(
                              '3',
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'مرحباً، ${user.fullName.split(' ').first}',
                    style: AppTheme.heading6.copyWith(color: AppColors.white),
                  ),
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: AppColors.patientGradient,
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Top Actions Row
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Notification Icon
                                GestureDetector(
                                  onTap: () {
                                    HapticFeedback.lightImpact();
                                    _showNotifications();
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: AppColors.white.withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Stack(
                                      children: [
                                        Icon(
                                          Icons.notifications_outlined,
                                          color: AppColors.white,
                                          size: 24,
                                        ),
                                        Positioned(
                                          right: 0,
                                          top: 0,
                                          child: Container(
                                            width: 8,
                                            height: 8,
                                            decoration: const BoxDecoration(
                                              color: AppColors.error,
                                              shape: BoxShape.circle,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                // Settings Icon
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: AppColors.white.withOpacity(0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.settings_outlined,
                                    color: AppColors.white,
                                    size: 24,
                                  ),
                                ),
                              ],
                            ),

                            const Spacer(),

                            // Patient Info Section
                            Row(
                              children: [
                                // Enhanced Avatar
                                Container(
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppColors.white,
                                      width: 3,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.2),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: CircleAvatar(
                                    radius: 35,
                                    backgroundColor: AppColors.white,
                                    child: Text(
                                      user.fullName.substring(0, 1),
                                      style: AppTheme.heading3.copyWith(
                                        color: AppColors.patientPrimary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 20),

                                // Patient Details
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        user.fullName,
                                        style: AppTheme.heading5.copyWith(
                                          color: AppColors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 8),

                                      // Health Stats Row
                                      Row(
                                        children: [
                                          _buildHealthStat(
                                            icon: Icons.cake_outlined,
                                            label: 'العمر',
                                            value: '${patient.age} سنة',
                                          ),
                                          const SizedBox(width: 20),
                                          _buildHealthStat(
                                            icon: Icons.accessibility_new,
                                            label: 'الحالة',
                                            value: _getDisabilityName(
                                              patient.primaryDisability,
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 12),

                                      // Quick Health Indicators
                                      Row(
                                        children: [
                                          _buildHealthIndicator(
                                            color: AppColors.success,
                                            label: 'الحالة العامة',
                                            status: 'جيدة',
                                          ),
                                          const SizedBox(width: 16),
                                          _buildHealthIndicator(
                                            color: AppColors.warning,
                                            label: 'المتابعة',
                                            status: 'مطلوبة',
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),

              // Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Quick Stats
                      _buildQuickStats(),
                      const SizedBox(height: 24),

                      // Quick Actions
                      Text('الإجراءات السريعة', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildQuickActions(),
                      const SizedBox(height: 24),

                      // Recent Activity
                      Text('النشاط الأخير', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildRecentActivity(),
                      const SizedBox(height: 24),

                      // Health Tips
                      Text('نصائح صحية', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildHealthTips(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.medical_services,
            title: 'الاستشارات',
            value: '0',
            subtitle: 'استشارة نشطة',
            color: AppColors.info,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.chat,
            title: 'المحادثات',
            value: '0',
            subtitle: 'محادثة جديدة',
            color: AppColors.success,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.schedule,
            title: 'المواعيد',
            value: '0',
            subtitle: 'موعد قادم',
            color: AppColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showComingSoon(title);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    value,
                    style: AppTheme.heading4.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    title,
                    style: AppTheme.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTheme.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      _QuickAction(
        icon: Icons.add_circle_outline,
        title: 'استشارة جديدة',
        subtitle: 'احجز استشارة مع مختص',
        color: AppColors.patientPrimary,
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const ConsultationsScreen(),
            ),
          );
        },
      ),
      _QuickAction(
        icon: Icons.folder_shared,
        title: 'ملفي الطبي',
        subtitle: 'عرض وإدارة ملفك الطبي',
        color: AppColors.info,
        onTap: () => Navigator.pushNamed(context, '/patient/medical-file'),
      ),
      _QuickAction(
        icon: Icons.location_on,
        title: 'المراكز القريبة',
        subtitle: 'ابحث عن مراكز قريبة',
        color: AppColors.success,
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const NearbyCentersScreen(),
            ),
          );
        },
      ),
      _QuickAction(
        icon: Icons.chat,
        title: 'المحادثات',
        subtitle: 'تواصل مع الأطباء',
        color: AppColors.warning,
        onTap: () => _showComingSoon('المحادثات'),
      ),
      _QuickAction(
        icon: Icons.shopping_cart,
        title: 'متجر المعدات',
        subtitle: 'تسوق المعدات الطبية',
        color: AppColors.error,
        onTap: () {
          HapticFeedback.lightImpact();
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const MedicalStoreScreen()),
          );
        },
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return GestureDetector(
          onTap: () {
            HapticFeedback.lightImpact();
            action.onTap();
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            child: Card(
              elevation: 6,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      action.color.withOpacity(0.1),
                      action.color.withOpacity(0.05),
                    ],
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: action.color.withOpacity(0.15),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(action.icon, size: 24, color: action.color),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        action.title,
                        style: AppTheme.labelLarge.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        action.subtitle,
                        style: AppTheme.caption.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildRecentActivity() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(Icons.history, size: 48, color: AppColors.grey400),
            const SizedBox(height: 16),
            Text(
              'لا يوجد نشاط حديث',
              style: AppTheme.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر هنا أحدث أنشطتك مثل الاستشارات والمواعيد',
              style: AppTheme.caption,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            PatientButton(
              text: 'ابدأ أول استشارة',
              onPressed: () {
                HapticFeedback.lightImpact();
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ConsultationsScreen(),
                  ),
                );
              },
              size: ButtonSize.small,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthTips() {
    final tips = [
      {
        'title': 'ممارسة الرياضة',
        'description': 'ممارسة التمارين المناسبة لحالتك يومياً',
        'icon': Icons.fitness_center,
      },
      {
        'title': 'التغذية الصحية',
        'description': 'تناول وجبات متوازنة وغنية بالفيتامينات',
        'icon': Icons.restaurant,
      },
      {
        'title': 'المتابعة الدورية',
        'description': 'احرص على المتابعة الدورية مع طبيبك',
        'icon': Icons.schedule,
      },
    ];

    return Column(
      children: tips.map((tip) {
        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.patientLight,
              child: Icon(
                tip['icon'] as IconData,
                color: AppColors.patientPrimary,
              ),
            ),
            title: Text(tip['title'] as String, style: AppTheme.labelLarge),
            subtitle: Text(
              tip['description'] as String,
              style: AppTheme.bodySmall,
            ),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const MedicalTipsScreen(),
                ),
              );
            },
          ),
        );
      }).toList(),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature ستكون متاحة قريباً'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  // Helper method for health stats in header
  Widget _buildHealthStat({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: AppColors.white.withOpacity(0.8), size: 16),
        const SizedBox(width: 4),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: AppTheme.caption.copyWith(
                color: AppColors.white.withOpacity(0.7),
                fontSize: 10,
              ),
            ),
            Text(
              value,
              style: AppTheme.bodySmall.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method for health indicators
  Widget _buildHealthIndicator({
    required Color color,
    required String label,
    required String status,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          ),
          const SizedBox(width: 6),
          Text(
            '$label: $status',
            style: AppTheme.caption.copyWith(
              color: AppColors.white,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get disability name
  String _getDisabilityName(dynamic disability) {
    return AppConstants.disabilityTypes[disability
            .toString()
            .split('.')
            .last] ??
        'غير محدد';
  }

  // Show notifications
  void _showNotifications() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.borderLight,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.notifications,
                    color: AppColors.patientPrimary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'الإشعارات',
                    style: AppTheme.heading5.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      // Mark all as read
                    },
                    child: Text(
                      'قراءة الكل',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppColors.patientPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Notifications List
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _getNotifications().length,
                itemBuilder: (context, index) {
                  final notification = _getNotifications()[index];
                  return _buildNotificationItem(notification);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Map<String, dynamic>> _getNotifications() {
    return [
      {
        'title': 'موعد جديد',
        'message': 'لديك موعد مع د. أحمد محمد غداً الساعة 10:00 صباحاً',
        'time': 'منذ 5 دقائق',
        'isRead': false,
        'type': 'appointment',
        'icon': Icons.calendar_today,
        'color': AppColors.info,
      },
      {
        'title': 'رسالة جديدة',
        'message': 'د. فاطمة علي أرسلت لك رسالة جديدة',
        'time': 'منذ 15 دقيقة',
        'isRead': false,
        'type': 'message',
        'icon': Icons.message,
        'color': AppColors.success,
      },
      {
        'title': 'تذكير دواء',
        'message': 'حان وقت تناول دواء الضغط',
        'time': 'منذ 30 دقيقة',
        'isRead': true,
        'type': 'medication',
        'icon': Icons.medication,
        'color': AppColors.warning,
      },
      {
        'title': 'نتائج التحاليل',
        'message': 'نتائج تحليل الدم جاهزة للمراجعة',
        'time': 'منذ ساعة',
        'isRead': true,
        'type': 'results',
        'icon': Icons.assignment,
        'color': AppColors.patientPrimary,
      },
      {
        'title': 'نصيحة طبية',
        'message': 'نصائح مهمة للحفاظ على صحة القلب',
        'time': 'منذ ساعتين',
        'isRead': true,
        'type': 'tip',
        'icon': Icons.lightbulb,
        'color': AppColors.info,
      },
    ];
  }

  Widget _buildNotificationItem(Map<String, dynamic> notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: notification['isRead']
            ? AppColors.surfaceLight
            : AppColors.patientPrimary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification['isRead']
              ? AppColors.borderLight
              : AppColors.patientPrimary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: notification['color'].withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              notification['icon'],
              color: notification['color'],
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        notification['title'],
                        style: AppTheme.bodyMedium.copyWith(
                          fontWeight: notification['isRead']
                              ? FontWeight.normal
                              : FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    if (!notification['isRead'])
                      Container(
                        width: 8,
                        height: 8,
                        decoration: const BoxDecoration(
                          color: AppColors.patientPrimary,
                          shape: BoxShape.circle,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  notification['message'],
                  style: AppTheme.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  notification['time'],
                  style: AppTheme.caption.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _QuickAction {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  _QuickAction({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });
}
