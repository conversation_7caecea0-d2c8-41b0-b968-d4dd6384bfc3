import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../models/patient_model.dart';
import '../models/doctor_model.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';

enum AuthState { initial, loading, authenticated, unauthenticated, error }

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();
  final DatabaseService _databaseService = DatabaseService();

  AuthState _state = AuthState.initial;
  User? _user;
  Patient? _patient;
  Doctor? _doctor;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthState get state => _state;
  User? get user => _user;
  Patient? get patient => _patient;
  Doctor? get doctor => _doctor;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _state == AuthState.authenticated;
  UserType? get userType => _user?.userType;

  // Initialize the provider
  Future<void> initialize() async {
    _setState(AuthState.loading);

    try {
      await _authService.initialize();

      if (_authService.isLoggedIn) {
        _user = _authService.currentUser;
        await _loadUserProfile();
        _setState(AuthState.authenticated);
      } else {
        _setState(AuthState.unauthenticated);
      }
    } catch (e) {
      _setError('خطأ في تهيئة التطبيق');
    }
  }

  // Load user profile based on user type
  Future<void> _loadUserProfile() async {
    if (_user == null) return;

    try {
      switch (_user!.userType) {
        case UserType.patient:
          // Try to load from database, if fails create demo data
          try {
            _patient = await _databaseService.getPatientByUserId(_user!.id);
          } catch (e) {
            _patient = _createDemoPatient();
          }
          break;
        case UserType.doctor:
          // Try to load from database, if fails create demo data
          try {
            _doctor = await _databaseService.getDoctorByUserId(_user!.id);
          } catch (e) {
            _doctor = _createDemoDoctor();
          }
          break;
        case UserType.admin:
          // Admin doesn't need additional profile data
          break;
      }
    } catch (e) {
      debugPrint('Error loading user profile: $e');
      // Create basic profile as fallback
      _createBasicProfile();
    }
  }

  // Register new user
  Future<bool> register({
    required String email,
    required String password,
    required String fullName,
    required UserType userType,
    String? phoneNumber,
  }) async {
    _setLoading(true);

    try {
      final result = await _authService.register(
        email: email,
        password: password,
        fullName: fullName,
        userType: userType,
        phoneNumber: phoneNumber,
      );

      if (result.success) {
        _user = result.user;
        await _loadUserProfile();
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(result.message ?? 'فشل في التسجيل');
        return false;
      }
    } catch (e) {
      _setError('خطأ في التسجيل');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Login user
  Future<bool> login({required String email, required String password}) async {
    _setLoading(true);

    try {
      final result = await _authService.login(email: email, password: password);

      if (result.success) {
        _user = result.user;
        await _loadUserProfile();
        _setState(AuthState.authenticated);
        return true;
      } else {
        _setError(result.message ?? 'فشل في تسجيل الدخول');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تسجيل الدخول');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout user
  Future<void> logout() async {
    _setLoading(true);

    try {
      await _authService.logout();
      _user = null;
      _patient = null;
      _doctor = null;
      _setState(AuthState.unauthenticated);
    } catch (e) {
      _setError('خطأ في تسجيل الخروج');
    } finally {
      _setLoading(false);
    }
  }

  // Create demo patient data
  Patient _createDemoPatient() {
    return Patient(
      userId: _user!.id,
      dateOfBirth: DateTime.now().subtract(const Duration(days: 365 * 30)),
      gender: Gender.male,
      nationalId: '**********',
      address: 'الرياض',
      city: 'الرياض',
      country: 'المملكة العربية السعودية',
      primaryDisability: DisabilityType.mobility,
      secondaryDisabilities: [],
      emergencyContact: EmergencyContact(
        name: 'أحمد محمد',
        phoneNumber: '+966501234567',
        relation: EmergencyContactRelation.sibling,
      ),
      medicalHistory: [
        MedicalHistory(
          condition: 'ضغط الدم',
          diagnosisDate: DateTime.now().subtract(const Duration(days: 365)),
          isActive: true,
        ),
      ],
      allergies: ['البنسلين'],
      currentMedications: ['أسبرين 100 مجم'],
      insuranceProvider: 'شركة التأمين الطبي',
      insuranceNumber: 'INS123456',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Create demo doctor data
  Doctor _createDemoDoctor() {
    return Doctor(
      userId: _user!.id,
      licenseNumber: 'DOC123456',
      primarySpecialization: Specialization.generalMedicine,
      secondarySpecializations: [],
      certifications: [
        Certification(
          name: 'شهادة الطب العام',
          issuingOrganization: 'الهيئة السعودية للتخصصات الصحية',
          issueDate: DateTime.now().subtract(const Duration(days: 365 * 3)),
        ),
      ],
      workExperience: [
        WorkExperience(
          position: 'طبيب عام',
          organization: 'مستشفى الملك فهد',
          startDate: DateTime.now().subtract(const Duration(days: 365 * 3)),
        ),
      ],
      languages: ['العربية', 'الإنجليزية'],
      clinicAddress: 'الرياض، المملكة العربية السعودية',
      consultationFee: 200.0,
      isAvailableForChat: true,
      status: DoctorStatus.verified,
      rating: 4.5,
      totalReviews: 150,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Create basic profile as fallback
  void _createBasicProfile() {
    switch (_user!.userType) {
      case UserType.patient:
        _patient = _createDemoPatient();
        break;
      case UserType.doctor:
        _doctor = _createDemoDoctor();
        break;
      case UserType.admin:
        // Admin doesn't need profile data
        break;
    }
  }

  // Demo login for testing
  Future<void> loginAsDemo(UserType userType) async {
    _setLoading(true);

    try {
      // Create demo user
      _user = User(
        email: 'demo_${userType.name}@khatwa.com',
        fullName: userType == UserType.patient ? 'مريض تجريبي' : 'طبيب تجريبي',
        phoneNumber: '+966501234567',
        userType: userType,
        status: UserStatus.active,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Create demo profile
      _createBasicProfile();

      // Set authenticated state
      _setState(AuthState.authenticated);
    } catch (e) {
      _setError('فشل في تشغيل وضع التجربة');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    _setLoading(true);

    try {
      final result = await _authService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (result.success) {
        _user = result.user;
        return true;
      } else {
        _setError(result.message ?? 'فشل في تغيير كلمة المرور');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تغيير كلمة المرور');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<bool> updateProfile({
    String? fullName,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    _setLoading(true);

    try {
      final result = await _authService.updateProfile(
        fullName: fullName,
        phoneNumber: phoneNumber,
        profileImageUrl: profileImageUrl,
      );

      if (result.success) {
        _user = result.user;
        notifyListeners();
        return true;
      } else {
        _setError(result.message ?? 'فشل في تحديث الملف الشخصي');
        return false;
      }
    } catch (e) {
      _setError('خطأ في تحديث الملف الشخصي');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Create patient profile
  Future<bool> createPatientProfile(Patient patient) async {
    _setLoading(true);

    try {
      await _databaseService.insertPatient(patient);
      _patient = patient;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في إنشاء الملف الطبي');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Create doctor profile
  Future<bool> createDoctorProfile(Doctor doctor) async {
    _setLoading(true);

    try {
      await _databaseService.insertDoctor(doctor);
      _doctor = doctor;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('فشل في إنشاء الملف المهني');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Check if user has permission
  bool hasPermission(String action) {
    return _authService.hasPermission(action);
  }

  // Get user role display name
  String getUserRoleDisplayName() {
    return _authService.getUserRoleDisplayName();
  }

  // Check if current user is verified
  Future<bool> isCurrentUserVerified() async {
    return await _authService.isCurrentUserVerified();
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Private helper methods
  void _setState(AuthState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String message) {
    _state = AuthState.error;
    _errorMessage = message;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Dispose method removed as it's not needed
}
