import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../models/doctor_model.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/gradient_button.dart';
import '../../widgets/common/loading_widget.dart';

class DoctorDashboard extends StatefulWidget {
  const DoctorDashboard({Key? key}) : super(key: key);

  @override
  State<DoctorDashboard> createState() => _DoctorDashboardState();
}

class _DoctorDashboardState extends State<DoctorDashboard> {
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final doctor = authProvider.doctor;
        final user = authProvider.user;

        if (doctor == null || user == null) {
          return const Scaffold(
            body: Center(
              child: DoctorLoadingWidget(message: 'جاري تحميل البيانات...'),
            ),
          );
        }

        // Check if doctor is verified
        if (doctor.status == DoctorStatus.pending) {
          return _buildPendingVerificationScreen();
        }

        if (doctor.status == DoctorStatus.rejected) {
          return _buildRejectedScreen();
        }

        return Scaffold(
          body: CustomScrollView(
            slivers: [
              // App Bar
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                backgroundColor: AppColors.doctorPrimary,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'د. ${user.fullName.split(' ').first}',
                    style: AppTheme.heading6.copyWith(color: AppColors.white),
                  ),
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: AppColors.doctorGradient,
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 30,
                                  backgroundColor: AppColors.white,
                                  child: Text(
                                    user.fullName.substring(0, 1),
                                    style: AppTheme.heading4.copyWith(
                                      color: AppColors.doctorPrimary,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'د. ${user.fullName}',
                                        style: AppTheme.heading5.copyWith(
                                          color: AppColors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        AppConstants.specializations[doctor
                                                .primarySpecialization
                                                .toString()
                                                .split('.')
                                                .last] ??
                                            '',
                                        style: AppTheme.bodyMedium.copyWith(
                                          color: AppColors.white.withOpacity(
                                            0.9,
                                          ),
                                        ),
                                      ),
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.star,
                                            color: AppColors.warning,
                                            size: 16,
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            '${doctor.rating.toStringAsFixed(1)} (${doctor.totalReviews} تقييم)',
                                            style: AppTheme.bodySmall.copyWith(
                                              color: AppColors.white
                                                  .withOpacity(0.8),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                if (doctor.isVerified)
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppColors.success,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.verified,
                                          color: AppColors.white,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          'موثق',
                                          style: AppTheme.labelSmall.copyWith(
                                            color: AppColors.white,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.notifications_outlined),
                    onPressed: () => _showComingSoon('الإشعارات'),
                  ),
                ],
              ),

              // Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Quick Stats
                      _buildQuickStats(doctor),
                      const SizedBox(height: 24),

                      // Quick Actions
                      Text('الإجراءات السريعة', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildQuickActions(),
                      const SizedBox(height: 24),

                      // Today's Schedule
                      Text('جدول اليوم', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildTodaySchedule(),
                      const SizedBox(height: 24),

                      // Recent Consultations
                      Text('الاستشارات الأخيرة', style: AppTheme.heading5),
                      const SizedBox(height: 16),
                      _buildRecentConsultations(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPendingVerificationScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('في انتظار التحقق'),
        backgroundColor: AppColors.warning,
        foregroundColor: AppColors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.hourglass_empty, size: 80, color: AppColors.warning),
              const SizedBox(height: 24),
              Text(
                'حسابك قيد المراجعة',
                style: AppTheme.heading4.copyWith(color: AppColors.warning),
              ),
              const SizedBox(height: 16),
              Text(
                'نشكرك على التسجيل في منصة خطوة. حسابك حالياً قيد المراجعة من قبل فريق الإدارة للتأكد من صحة البيانات المهنية.',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'ستتلقى إشعاراً عند تفعيل حسابك خلال 24-48 ساعة.',
                style: AppTheme.bodySmall.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              DoctorButton(
                text: 'تحديث الصفحة',
                onPressed: () {
                  // Refresh the auth provider
                  Provider.of<AuthProvider>(
                    context,
                    listen: false,
                  ).initialize();
                },
                icon: const Icon(Icons.refresh, color: AppColors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRejectedScreen() {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تم رفض الطلب'),
        backgroundColor: AppColors.error,
        foregroundColor: AppColors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.cancel, size: 80, color: AppColors.error),
              const SizedBox(height: 24),
              Text(
                'تم رفض طلب التحقق',
                style: AppTheme.heading4.copyWith(color: AppColors.error),
              ),
              const SizedBox(height: 16),
              Text(
                'نأسف لإبلاغك أنه تم رفض طلب التحقق من حسابك. يرجى مراجعة البيانات المرسلة والتأكد من صحتها.',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              DoctorButton(
                text: 'التواصل مع الدعم',
                onPressed: () => _showComingSoon('الدعم الفني'),
                icon: const Icon(Icons.support_agent, color: AppColors.white),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickStats(doctor) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            icon: Icons.assignment,
            title: 'الاستشارات',
            value: '0',
            subtitle: 'استشارة نشطة',
            color: AppColors.info,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.people,
            title: 'المرضى',
            value: '0',
            subtitle: 'مريض جديد',
            color: AppColors.success,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            icon: Icons.schedule,
            title: 'المواعيد',
            value: '0',
            subtitle: 'موعد اليوم',
            color: AppColors.warning,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required String subtitle,
    required Color color,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showComingSoon(title);
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [color.withOpacity(0.1), color.withOpacity(0.05)],
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    value,
                    style: AppTheme.heading4.copyWith(
                      color: color,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    title,
                    style: AppTheme.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: AppTheme.caption.copyWith(
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    final actions = [
      _QuickAction(
        icon: Icons.assignment_add,
        title: 'الاستشارات الجديدة',
        subtitle: 'مراجعة الطلبات الجديدة',
        color: AppColors.doctorPrimary,
        onTap: () => _showComingSoon('الاستشارات'),
      ),
      _QuickAction(
        icon: Icons.chat,
        title: 'المحادثات',
        subtitle: 'التواصل مع المرضى',
        color: AppColors.info,
        onTap: () => _showComingSoon('المحادثات'),
      ),
      _QuickAction(
        icon: Icons.schedule,
        title: 'إدارة الجدول',
        subtitle: 'تنظيم المواعيد',
        color: AppColors.warning,
        onTap: () => _showComingSoon('الجدول'),
      ),
      _QuickAction(
        icon: Icons.person,
        title: 'الملف المهني',
        subtitle: 'عرض وتحديث الملف',
        color: AppColors.success,
        onTap: () => Navigator.pushNamed(context, '/doctor/profile'),
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1.2,
      ),
      itemCount: actions.length,
      itemBuilder: (context, index) {
        final action = actions[index];
        return Card(
          child: InkWell(
            onTap: action.onTap,
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(action.icon, size: 32, color: action.color),
                  const SizedBox(height: 8),
                  Text(
                    action.title,
                    style: AppTheme.labelLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    action.subtitle,
                    style: AppTheme.caption,
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTodaySchedule() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Icon(Icons.today, color: AppColors.doctorPrimary),
                const SizedBox(width: 8),
                Text('لا توجد مواعيد اليوم', style: AppTheme.bodyMedium),
                const Spacer(),
                TextButton(
                  onPressed: () => _showComingSoon('إدارة الجدول'),
                  child: const Text('عرض الكل'),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك إدارة جدولك وإضافة مواعيد جديدة',
              style: AppTheme.caption,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentConsultations() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(Icons.assignment_outlined, size: 48, color: AppColors.grey400),
            const SizedBox(height: 16),
            Text(
              'لا توجد استشارات حديثة',
              style: AppTheme.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ستظهر هنا الاستشارات الأخيرة والطلبات الجديدة',
              style: AppTheme.caption,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            DoctorButton(
              text: 'تفعيل الاستشارات',
              onPressed: () => _showComingSoon('إعدادات الاستشارة'),
              size: ButtonSize.small,
            ),
          ],
        ),
      ),
    );
  }

  void _showComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة $feature ستكون متاحة قريباً'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

class _QuickAction {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  _QuickAction({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });
}
