class AppNotification {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final bool isRead;
  final DateTime timestamp;
  final String? actionUrl;
  final NotificationPriority priority;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? soundUrl;

  AppNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.isRead = false,
    required this.timestamp,
    this.actionUrl,
    this.priority = NotificationPriority.medium,
    this.data,
    this.imageUrl,
    this.soundUrl,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == 'NotificationType.${json['type']}',
        orElse: () => NotificationType.general,
      ),
      isRead: json['isRead'] ?? false,
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      actionUrl: json['actionUrl'],
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString() == 'NotificationPriority.${json['priority']}',
        orElse: () => NotificationPriority.medium,
      ),
      data: json['data'],
      imageUrl: json['imageUrl'],
      soundUrl: json['soundUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.toString().split('.').last,
      'isRead': isRead,
      'timestamp': timestamp.toIso8601String(),
      'actionUrl': actionUrl,
      'priority': priority.toString().split('.').last,
      'data': data,
      'imageUrl': imageUrl,
      'soundUrl': soundUrl,
    };
  }

  AppNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    bool? isRead,
    DateTime? timestamp,
    String? actionUrl,
    NotificationPriority? priority,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? soundUrl,
  }) {
    return AppNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      timestamp: timestamp ?? this.timestamp,
      actionUrl: actionUrl ?? this.actionUrl,
      priority: priority ?? this.priority,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      soundUrl: soundUrl ?? this.soundUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppNotification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AppNotification(id: $id, title: $title, type: $type, isRead: $isRead)';
  }

  // Helper methods
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'منذ $weeks ${weeks == 1 ? 'أسبوع' : 'أسابيع'}';
    } else {
      final months = (difference.inDays / 30).floor();
      return 'منذ $months ${months == 1 ? 'شهر' : 'أشهر'}';
    }
  }

  String get formattedDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final notificationDate = DateTime(timestamp.year, timestamp.month, timestamp.day);
    
    if (notificationDate == today) {
      return 'اليوم';
    } else if (notificationDate == today.subtract(const Duration(days: 1))) {
      return 'أمس';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  String get priorityText {
    switch (priority) {
      case NotificationPriority.low:
        return 'منخفضة';
      case NotificationPriority.medium:
        return 'متوسطة';
      case NotificationPriority.high:
        return 'عالية';
      case NotificationPriority.urgent:
        return 'عاجلة';
    }
  }

  String get typeText {
    switch (type) {
      case NotificationType.consultation:
        return 'استشارة';
      case NotificationType.appointment:
        return 'موعد';
      case NotificationType.tip:
        return 'نصيحة';
      case NotificationType.reminder:
        return 'تذكير';
      case NotificationType.general:
        return 'عام';
    }
  }

  bool get isUrgent => priority == NotificationPriority.urgent;
  bool get isHigh => priority == NotificationPriority.high;
  bool get isRecent => DateTime.now().difference(timestamp).inHours < 24;
  bool get hasAction => actionUrl != null && actionUrl!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasSound => soundUrl != null && soundUrl!.isNotEmpty;

  // Static methods for filtering and sorting
  static List<AppNotification> filterByType(List<AppNotification> notifications, NotificationType type) {
    return notifications.where((notification) => notification.type == type).toList();
  }

  static List<AppNotification> filterByRead(List<AppNotification> notifications, bool isRead) {
    return notifications.where((notification) => notification.isRead == isRead).toList();
  }

  static List<AppNotification> filterByPriority(List<AppNotification> notifications, NotificationPriority priority) {
    return notifications.where((notification) => notification.priority == priority).toList();
  }

  static List<AppNotification> filterByDateRange(List<AppNotification> notifications, DateTime start, DateTime end) {
    return notifications.where((notification) => 
        notification.timestamp.isAfter(start) && notification.timestamp.isBefore(end)).toList();
  }

  static List<AppNotification> filterByRecent(List<AppNotification> notifications, {int hours = 24}) {
    final cutoff = DateTime.now().subtract(Duration(hours: hours));
    return notifications.where((notification) => notification.timestamp.isAfter(cutoff)).toList();
  }

  static List<AppNotification> sortByDate(List<AppNotification> notifications, {bool ascending = false}) {
    List<AppNotification> sorted = List.from(notifications);
    sorted.sort((a, b) => ascending 
        ? a.timestamp.compareTo(b.timestamp)
        : b.timestamp.compareTo(a.timestamp));
    return sorted;
  }

  static List<AppNotification> sortByPriority(List<AppNotification> notifications) {
    List<AppNotification> sorted = List.from(notifications);
    sorted.sort((a, b) {
      final priorityOrder = {
        NotificationPriority.urgent: 4,
        NotificationPriority.high: 3,
        NotificationPriority.medium: 2,
        NotificationPriority.low: 1,
      };
      return priorityOrder[b.priority]!.compareTo(priorityOrder[a.priority]!);
    });
    return sorted;
  }

  static List<AppNotification> sortByReadStatus(List<AppNotification> notifications) {
    List<AppNotification> sorted = List.from(notifications);
    sorted.sort((a, b) => a.isRead ? 1 : -1);
    return sorted;
  }

  static List<AppNotification> searchByKeyword(List<AppNotification> notifications, String keyword) {
    if (keyword.isEmpty) return notifications;
    final lowerKeyword = keyword.toLowerCase();
    
    return notifications.where((notification) => 
        notification.title.toLowerCase().contains(lowerKeyword) ||
        notification.message.toLowerCase().contains(lowerKeyword)
    ).toList();
  }

  static Map<NotificationType, int> getTypeStats(List<AppNotification> notifications) {
    Map<NotificationType, int> stats = {};
    for (var notification in notifications) {
      stats[notification.type] = (stats[notification.type] ?? 0) + 1;
    }
    return stats;
  }

  static Map<NotificationPriority, int> getPriorityStats(List<AppNotification> notifications) {
    Map<NotificationPriority, int> stats = {};
    for (var notification in notifications) {
      stats[notification.priority] = (stats[notification.priority] ?? 0) + 1;
    }
    return stats;
  }

  static int getUnreadCount(List<AppNotification> notifications) {
    return notifications.where((notification) => !notification.isRead).length;
  }

  static List<AppNotification> getUnread(List<AppNotification> notifications) {
    return notifications.where((notification) => !notification.isRead).toList();
  }

  static List<AppNotification> getRead(List<AppNotification> notifications) {
    return notifications.where((notification) => notification.isRead).toList();
  }

  static List<AppNotification> markAllAsRead(List<AppNotification> notifications) {
    return notifications.map((notification) => notification.copyWith(isRead: true)).toList();
  }

  static List<AppNotification> markAllAsUnread(List<AppNotification> notifications) {
    return notifications.map((notification) => notification.copyWith(isRead: false)).toList();
  }
}

// Enums
enum NotificationType {
  consultation,
  appointment,
  tip,
  reminder,
  general,
}

enum NotificationPriority {
  low,
  medium,
  high,
  urgent,
}

// Extensions
extension NotificationTypeExtension on NotificationType {
  String get arabicName {
    switch (this) {
      case NotificationType.consultation:
        return 'استشارة';
      case NotificationType.appointment:
        return 'موعد';
      case NotificationType.tip:
        return 'نصيحة';
      case NotificationType.reminder:
        return 'تذكير';
      case NotificationType.general:
        return 'عام';
    }
  }

  String get iconName {
    switch (this) {
      case NotificationType.consultation:
        return 'medical_services';
      case NotificationType.appointment:
        return 'schedule';
      case NotificationType.tip:
        return 'lightbulb';
      case NotificationType.reminder:
        return 'alarm';
      case NotificationType.general:
        return 'info';
    }
  }
}

extension NotificationPriorityExtension on NotificationPriority {
  String get arabicName {
    switch (this) {
      case NotificationPriority.low:
        return 'منخفضة';
      case NotificationPriority.medium:
        return 'متوسطة';
      case NotificationPriority.high:
        return 'عالية';
      case NotificationPriority.urgent:
        return 'عاجلة';
    }
  }

  String get colorName {
    switch (this) {
      case NotificationPriority.low:
        return 'success';
      case NotificationPriority.medium:
        return 'info';
      case NotificationPriority.high:
        return 'warning';
      case NotificationPriority.urgent:
        return 'error';
    }
  }
}
