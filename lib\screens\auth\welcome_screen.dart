import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_constants.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/gradient_button.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
        );

    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Gradient Background
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [AppColors.patientLight, AppColors.white],
              ),
            ),
          ),
          // Content
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    children: [
                      Expanded(
                        flex: 3,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // App Logo
                            Container(
                              width: 150,
                              height: 150,
                              decoration: BoxDecoration(
                                color: AppColors.white,
                                borderRadius: BorderRadius.circular(30),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.patientPrimary.withOpacity(
                                      0.2,
                                    ),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(30),
                                child: Image.asset(
                                  'assets/images/logo.png',
                                  fit: BoxFit.contain,
                                ),
                              ),
                            ),
                            const SizedBox(height: 32),
                            // App Name
                            Text(
                              AppConstants.appName,
                              style: AppTheme.heading1.copyWith(
                                color: AppColors.patientPrimary,
                                fontFamily: AppTheme.decorativeFontFamily,
                              ),
                            ),
                            const SizedBox(height: 16),
                            // App Tagline
                            Text(
                              AppConstants.appTagline,
                              style: AppTheme.bodyLarge.copyWith(
                                color: AppColors.textSecondary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 32),
                            // Description
                            Text(
                              'منصة شاملة تربط بين المرضى والمختصين لتقديم أفضل الخدمات الاستشارية والعلاجية',
                              style: AppTheme.bodyMedium.copyWith(
                                color: AppColors.textSecondary,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // User Type Selection
                            Text(
                              'اختر نوع حسابك',
                              style: AppTheme.heading5.copyWith(
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const SizedBox(height: 24),
                            // Patient Button
                            _buildAnimatedButton(
                              child: PatientButton(
                                text: 'مريض',
                                onPressed: () => _navigateWithAnimation(
                                  context,
                                  '/login',
                                  'patient',
                                ),
                                size: ButtonSize.large,
                                width: double.infinity,
                                icon: const Icon(
                                  Icons.person,
                                  color: AppColors.white,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Doctor Button
                            _buildAnimatedButton(
                              child: DoctorButton(
                                text: 'طبيب / مختص',
                                onPressed: () => _navigateWithAnimation(
                                  context,
                                  '/login',
                                  'doctor',
                                ),
                                size: ButtonSize.large,
                                width: double.infinity,
                                icon: const Icon(
                                  Icons.medical_services,
                                  color: AppColors.white,
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Admin Button
                            _buildAnimatedButton(
                              child: AdminButton(
                                text: 'إدارة',
                                onPressed: () => _navigateWithAnimation(
                                  context,
                                  '/login',
                                  'admin',
                                ),
                                size: ButtonSize.large,
                                width: double.infinity,
                                icon: const Icon(
                                  Icons.admin_panel_settings,
                                  color: AppColors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Footer
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Column(
                          children: [
                            Text(
                              'الإصدار ${AppConstants.appVersion}',
                              style: AppTheme.caption,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '© 2024 خطوة. جميع الحقوق محفوظة',
                              style: AppTheme.caption,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedButton({required Widget child}) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 200),
      tween: Tween(begin: 1.0, end: 1.0),
      builder: (context, scale, child) {
        return Transform.scale(scale: scale, child: child);
      },
      child: child,
    );
  }

  void _navigateWithAnimation(
    BuildContext context,
    String route,
    String argument,
  ) {
    // Add haptic feedback
    HapticFeedback.lightImpact();

    // Simple navigation with arguments
    Navigator.pushNamed(context, route, arguments: argument);
  }
}
