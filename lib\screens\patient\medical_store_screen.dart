import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../models/medical_product_model.dart';

class MedicalStoreScreen extends StatefulWidget {
  const MedicalStoreScreen({super.key});

  @override
  State<MedicalStoreScreen> createState() => _MedicalStoreScreenState();
}

class _MedicalStoreScreenState extends State<MedicalStoreScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<MedicalProduct> _products = [];
  List<MedicalProduct> _filteredProducts = [];
  List<MedicalProduct> _cartItems = [];
  String _selectedCategory = 'الكل';
  bool _isLoading = true;

  final List<String> _categories = [
    'الكل',
    'أجهزة قياس',
    'مستلزمات طبية',
    'أدوات مساعدة',
    'معدات تأهيل',
    'مكملات غذائية',
    'عناية شخصية',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadProducts();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadProducts() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _products = _getMockProducts();
          _filteredProducts = _products;
          _isLoading = false;
        });
      }
    });
  }

  List<MedicalProduct> _getMockProducts() {
    return [
      MedicalProduct(
        id: '1',
        name: 'جهاز قياس ضغط الدم الرقمي',
        description:
            'جهاز قياس ضغط الدم الرقمي الأوتوماتيكي مع شاشة LCD كبيرة وذاكرة لحفظ القراءات',
        category: 'أجهزة قياس',
        price: 299.99,
        originalPrice: 399.99,
        imageUrl: 'assets/images/blood_pressure_monitor.jpg',
        rating: 4.5,
        reviewCount: 128,
        isInStock: true,
        stockQuantity: 15,
        brand: 'Omron',
        features: [
          'شاشة LCD كبيرة',
          'ذاكرة لـ 60 قراءة',
          'كشف عدم انتظام ضربات القلب',
          'سهل الاستخدام',
          'ضمان سنتين',
        ],
        specifications: {
          'النوع': 'رقمي أوتوماتيكي',
          'حجم الكفة': '22-42 سم',
          'الدقة': '±3 mmHg',
          'المصدر': 'بطاريات AA أو محول كهربائي',
          'الوزن': '300 جرام',
        },
      ),
      MedicalProduct(
        id: '2',
        name: 'جهاز قياس السكر مع شرائط',
        description: 'جهاز قياس السكر في الدم مع 50 شريط اختبار ومشارط معقمة',
        category: 'أجهزة قياس',
        price: 149.99,
        originalPrice: 199.99,
        imageUrl: 'assets/images/glucose_meter.jpg',
        rating: 4.3,
        reviewCount: 89,
        isInStock: true,
        stockQuantity: 25,
        brand: 'Accu-Chek',
        features: [
          'نتائج سريعة في 5 ثوان',
          'عينة دم صغيرة',
          'ذاكرة لـ 500 نتيجة',
          'اتصال بالهاتف الذكي',
          'شرائط اختبار مجانية',
        ],
        specifications: {
          'وقت الاختبار': '5 ثوان',
          'حجم العينة': '0.6 ميكرولتر',
          'نطاق القياس': '20-600 mg/dL',
          'درجة حرارة التشغيل': '5-45°C',
          'البطارية': 'ليثيوم 3V',
        },
      ),
      MedicalProduct(
        id: '3',
        name: 'كرسي متحرك قابل للطي',
        description:
            'كرسي متحرك خفيف الوزن وقابل للطي مع مساند مريحة وعجلات عالية الجودة',
        category: 'أدوات مساعدة',
        price: 1299.99,
        originalPrice: 1599.99,
        imageUrl: 'assets/images/wheelchair.jpg',
        rating: 4.7,
        reviewCount: 45,
        isInStock: true,
        stockQuantity: 8,
        brand: 'Drive Medical',
        features: [
          'قابل للطي والحمل',
          'خفيف الوزن',
          'مساند قابلة للإزالة',
          'فرامل يدوية آمنة',
          'مقعد مبطن مريح',
        ],
        specifications: {
          'الوزن': '15 كيلوجرام',
          'سعة التحميل': '120 كيلوجرام',
          'عرض المقعد': '46 سم',
          'المادة': 'ألومنيوم عالي الجودة',
          'قطر العجلات': '60 سم',
        },
      ),
      MedicalProduct(
        id: '4',
        name: 'جهاز استنشاق البخار',
        description:
            'جهاز استنشاق البخار الطبي لعلاج مشاكل الجهاز التنفسي والجيوب الأنفية',
        category: 'معدات تأهيل',
        price: 89.99,
        originalPrice: 119.99,
        imageUrl: 'assets/images/nebulizer.jpg',
        rating: 4.2,
        reviewCount: 67,
        isInStock: true,
        stockQuantity: 20,
        brand: 'Philips',
        features: [
          'تقنية الضغط المتقدمة',
          'هادئ في التشغيل',
          'أقنعة متعددة الأحجام',
          'سهل التنظيف',
          'محمول وخفيف',
        ],
        specifications: {
          'معدل الرذاذ': '0.3 مل/دقيقة',
          'حجم الجسيمات': '1-5 ميكرون',
          'مستوى الضوضاء': 'أقل من 60 ديسيبل',
          'سعة الخزان': '6 مل',
          'الطاقة': '220V AC',
        },
      ),
      MedicalProduct(
        id: '5',
        name: 'مقياس حرارة رقمي بالأشعة تحت الحمراء',
        description:
            'مقياس حرارة رقمي بدون لمس يعمل بالأشعة تحت الحمراء مع قراءة فورية',
        category: 'أجهزة قياس',
        price: 79.99,
        originalPrice: 99.99,
        imageUrl: 'assets/images/thermometer.jpg',
        rating: 4.4,
        reviewCount: 156,
        isInStock: true,
        stockQuantity: 30,
        brand: 'Braun',
        features: [
          'قياس بدون لمس',
          'نتائج فورية',
          'إنذار للحمى',
          'ذاكرة للقراءات السابقة',
          'شاشة ملونة',
        ],
        specifications: {
          'دقة القياس': '±0.2°C',
          'المدى': '32-42.9°C',
          'المسافة': '3-5 سم',
          'وقت القياس': 'ثانية واحدة',
          'البطارية': 'AAA × 2',
        },
      ),
    ];
  }

  void _filterProducts(String category) {
    setState(() {
      _selectedCategory = category;
      if (category == 'الكل') {
        _filteredProducts = _products;
      } else {
        _filteredProducts = _products
            .where((product) => product.category == category)
            .toList();
      }
    });
  }

  void _addToCart(MedicalProduct product) {
    setState(() {
      final existingIndex = _cartItems.indexWhere(
        (item) => item.id == product.id,
      );
      if (existingIndex >= 0) {
        // Product already in cart, increase quantity
        _cartItems[existingIndex] = _cartItems[existingIndex].copyWith(
          quantity: _cartItems[existingIndex].quantity + 1,
        );
      } else {
        // Add new product to cart
        _cartItems.add(product.copyWith(quantity: 1));
      }
    });

    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إضافة ${product.name} إلى السلة'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  double get _cartTotal {
    return _cartItems.fold(
      0.0,
      (total, item) => total + (item.price * item.quantity),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.patientPrimary,
        foregroundColor: AppColors.white,
        elevation: 0,
        title: Text(
          'متجر المعدات الطبية',
          style: AppTheme.heading6.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          Stack(
            children: [
              IconButton(
                onPressed: () => _showCart(),
                icon: const Icon(Icons.shopping_cart),
                tooltip: 'السلة',
              ),
              if (_cartItems.isNotEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: const BoxDecoration(
                      color: AppColors.error,
                      shape: BoxShape.circle,
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '${_cartItems.length}',
                      style: const TextStyle(
                        color: AppColors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          IconButton(
            onPressed: () => _showSearchDialog(),
            icon: const Icon(Icons.search),
            tooltip: 'البحث',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Categories Filter
            Container(
              height: 60,
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = category == _selectedCategory;

                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (_) => _filterProducts(category),
                      backgroundColor: AppColors.surfaceLight,
                      selectedColor: AppColors.patientPrimary.withValues(
                        alpha: 0.2,
                      ),
                      checkmarkColor: AppColors.patientPrimary,
                      labelStyle: AppTheme.bodySmall.copyWith(
                        color: isSelected
                            ? AppColors.patientPrimary
                            : AppColors.textSecondary,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                      side: BorderSide(
                        color: isSelected
                            ? AppColors.patientPrimary
                            : AppColors.borderLight,
                        width: 1,
                      ),
                    ),
                  );
                },
              ),
            ),

            // Products Grid
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.patientPrimary,
                      ),
                    )
                  : _filteredProducts.isEmpty
                  ? _buildEmptyState()
                  : GridView.builder(
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 0.7,
                            crossAxisSpacing: 16,
                            mainAxisSpacing: 16,
                          ),
                      itemCount: _filteredProducts.length,
                      itemBuilder: (context, index) {
                        return _buildProductCard(_filteredProducts[index]);
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: _cartItems.isNotEmpty
          ? FloatingActionButton.extended(
              onPressed: () => _showCart(),
              backgroundColor: AppColors.patientPrimary,
              foregroundColor: AppColors.white,
              icon: const Icon(Icons.shopping_cart),
              label: Text('السلة (${_cartItems.length})'),
            )
          : null,
    );
  }

  Widget _buildProductCard(MedicalProduct product) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Image
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
                image: DecorationImage(
                  image: AssetImage(product.imageUrl),
                  fit: BoxFit.cover,
                  onError: (exception, stackTrace) {
                    // Handle missing image gracefully
                  },
                ),
              ),
              child: Stack(
                children: [
                  // Discount badge
                  if (product.hasDiscount)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppColors.error,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          product.formattedDiscount,
                          style: AppTheme.caption.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                  // Stock status
                  if (!product.isInStock || product.isLowStock)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: product.isInStock
                              ? AppColors.warning
                              : AppColors.error,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          product.stockStatus,
                          style: AppTheme.caption.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Product Info
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Brand
                  Text(
                    product.brand,
                    style: AppTheme.caption.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Product Name
                  Text(
                    product.name,
                    style: AppTheme.bodySmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Rating
                  Row(
                    children: [
                      Icon(Icons.star, color: AppColors.warning, size: 14),
                      const SizedBox(width: 4),
                      Text(
                        product.formattedRating,
                        style: AppTheme.caption.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '(${product.reviewCount})',
                        style: AppTheme.caption.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Price
                  Row(
                    children: [
                      Text(
                        product.formattedPrice,
                        style: AppTheme.bodyMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.patientPrimary,
                        ),
                      ),
                      if (product.hasDiscount) ...[
                        const SizedBox(width: 8),
                        Text(
                          product.formattedOriginalPrice,
                          style: AppTheme.caption.copyWith(
                            color: AppColors.textSecondary,
                            decoration: TextDecoration.lineThrough,
                          ),
                        ),
                      ],
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Add to Cart Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: product.isInStock
                          ? () => _addToCart(product)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.patientPrimary,
                        foregroundColor: AppColors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        product.isInStock ? 'إضافة للسلة' : 'غير متوفر',
                        style: AppTheme.caption.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_cart_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد منتجات متاحة',
            style: AppTheme.heading6.copyWith(color: AppColors.textSecondary),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفئة أو البحث عن منتجات أخرى',
            style: AppTheme.bodyMedium.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showCart() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.borderLight,
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.shopping_cart,
                    color: AppColors.patientPrimary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'سلة التسوق',
                    style: AppTheme.heading5.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  if (_cartItems.isNotEmpty)
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _cartItems.clear();
                        });
                        Navigator.pop(context);
                      },
                      child: Text(
                        'إفراغ السلة',
                        style: AppTheme.bodySmall.copyWith(
                          color: AppColors.error,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Cart items
            Expanded(
              child: _cartItems.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.shopping_cart_outlined,
                            size: 48,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'السلة فارغة',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _cartItems.length,
                      itemBuilder: (context, index) {
                        final item = _cartItems[index];
                        return _buildCartItem(item, index);
                      },
                    ),
            ),

            // Total and Checkout
            if (_cartItems.isNotEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.surfaceLight,
                  border: Border(
                    top: BorderSide(color: AppColors.borderLight, width: 1),
                  ),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'المجموع:',
                          style: AppTheme.heading6.copyWith(
                            color: AppColors.textPrimary,
                          ),
                        ),
                        Text(
                          '${_cartTotal.toStringAsFixed(2)} ر.س',
                          style: AppTheme.heading5.copyWith(
                            color: AppColors.patientPrimary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () => _checkout(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.patientPrimary,
                          foregroundColor: AppColors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'إتمام الطلب',
                          style: AppTheme.buttonMedium.copyWith(
                            color: AppColors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartItem(MedicalProduct item, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.borderLight, width: 1),
      ),
      child: Row(
        children: [
          // Product Image
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: AssetImage(item.imageUrl),
                fit: BoxFit.cover,
                onError: (exception, stackTrace) {
                  // Handle missing image gracefully
                },
              ),
            ),
          ),

          const SizedBox(width: 12),

          // Product Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  item.formattedPrice,
                  style: AppTheme.bodySmall.copyWith(
                    color: AppColors.patientPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Quantity Controls
          Row(
            children: [
              IconButton(
                onPressed: () => _decreaseQuantity(index),
                icon: const Icon(Icons.remove_circle_outline),
                color: AppColors.textSecondary,
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
              Text(
                '${item.quantity}',
                style: AppTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              IconButton(
                onPressed: () => _increaseQuantity(index),
                icon: const Icon(Icons.add_circle_outline),
                color: AppColors.patientPrimary,
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _increaseQuantity(int index) {
    setState(() {
      _cartItems[index] = _cartItems[index].copyWith(
        quantity: _cartItems[index].quantity + 1,
      );
    });
  }

  void _decreaseQuantity(int index) {
    if (_cartItems[index].quantity > 1) {
      setState(() {
        _cartItems[index] = _cartItems[index].copyWith(
          quantity: _cartItems[index].quantity - 1,
        );
      });
    } else {
      setState(() {
        _cartItems.removeAt(index);
      });
    }
  }

  void _checkout() {
    Navigator.pop(context);
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('صفحة الدفع ستكون متاحة قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في المنتجات'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'اكتب اسم المنتج أو الماركة...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            // Implement search functionality
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }
}
