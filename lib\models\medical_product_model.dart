class MedicalProduct {
  final String id;
  final String name;
  final String description;
  final String category;
  final double price;
  final double? originalPrice;
  final String imageUrl;
  final double rating;
  final int reviewCount;
  final bool isInStock;
  final int stockQuantity;
  final String brand;
  final List<String> features;
  final Map<String, String> specifications;
  final int quantity;
  final bool isFavorite;
  final List<String> tags;
  final DateTime? createdAt;
  final String? manufacturerCountry;
  final String? warranty;
  final bool requiresPrescription;

  MedicalProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.price,
    this.originalPrice,
    required this.imageUrl,
    this.rating = 0.0,
    this.reviewCount = 0,
    this.isInStock = true,
    this.stockQuantity = 0,
    required this.brand,
    this.features = const [],
    this.specifications = const {},
    this.quantity = 0,
    this.isFavorite = false,
    this.tags = const [],
    this.createdAt,
    this.manufacturerCountry,
    this.warranty,
    this.requiresPrescription = false,
  });

  factory MedicalProduct.fromJson(Map<String, dynamic> json) {
    return MedicalProduct(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      originalPrice: json['originalPrice']?.toDouble(),
      imageUrl: json['imageUrl'] ?? '',
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['reviewCount'] ?? 0,
      isInStock: json['isInStock'] ?? true,
      stockQuantity: json['stockQuantity'] ?? 0,
      brand: json['brand'] ?? '',
      features: List<String>.from(json['features'] ?? []),
      specifications: Map<String, String>.from(json['specifications'] ?? {}),
      quantity: json['quantity'] ?? 0,
      isFavorite: json['isFavorite'] ?? false,
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'])
          : null,
      manufacturerCountry: json['manufacturerCountry'],
      warranty: json['warranty'],
      requiresPrescription: json['requiresPrescription'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'price': price,
      'originalPrice': originalPrice,
      'imageUrl': imageUrl,
      'rating': rating,
      'reviewCount': reviewCount,
      'isInStock': isInStock,
      'stockQuantity': stockQuantity,
      'brand': brand,
      'features': features,
      'specifications': specifications,
      'quantity': quantity,
      'isFavorite': isFavorite,
      'tags': tags,
      'createdAt': createdAt?.toIso8601String(),
      'manufacturerCountry': manufacturerCountry,
      'warranty': warranty,
      'requiresPrescription': requiresPrescription,
    };
  }

  MedicalProduct copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    double? price,
    double? originalPrice,
    String? imageUrl,
    double? rating,
    int? reviewCount,
    bool? isInStock,
    int? stockQuantity,
    String? brand,
    List<String>? features,
    Map<String, String>? specifications,
    int? quantity,
    bool? isFavorite,
    List<String>? tags,
    DateTime? createdAt,
    String? manufacturerCountry,
    String? warranty,
    bool? requiresPrescription,
  }) {
    return MedicalProduct(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      imageUrl: imageUrl ?? this.imageUrl,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isInStock: isInStock ?? this.isInStock,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      brand: brand ?? this.brand,
      features: features ?? this.features,
      specifications: specifications ?? this.specifications,
      quantity: quantity ?? this.quantity,
      isFavorite: isFavorite ?? this.isFavorite,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      manufacturerCountry: manufacturerCountry ?? this.manufacturerCountry,
      warranty: warranty ?? this.warranty,
      requiresPrescription: requiresPrescription ?? this.requiresPrescription,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MedicalProduct && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MedicalProduct(id: $id, name: $name, category: $category, price: $price)';
  }

  // Helper methods
  bool get hasDiscount => originalPrice != null && originalPrice! > price;

  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((originalPrice! - price) / originalPrice!) * 100;
  }

  String get formattedPrice {
    return '${price.toStringAsFixed(2)} ر.س';
  }

  String get formattedOriginalPrice {
    if (originalPrice == null) return '';
    return '${originalPrice!.toStringAsFixed(2)} ر.س';
  }

  String get formattedDiscount {
    if (!hasDiscount) return '';
    return '${discountPercentage.toStringAsFixed(0)}% خصم';
  }

  String get formattedRating {
    return rating.toStringAsFixed(1);
  }

  String get stockStatus {
    if (!isInStock) return 'غير متوفر';
    if (stockQuantity <= 5) return 'كمية محدودة';
    return 'متوفر';
  }

  bool get isLowStock => isInStock && stockQuantity <= 5;

  bool get isPopular => reviewCount > 50 && rating >= 4.0;

  bool get isNew {
    if (createdAt == null) return false;
    final now = DateTime.now();
    final difference = now.difference(createdAt!);
    return difference.inDays <= 30;
  }

  double get totalPrice => price * quantity;

  String get formattedTotalPrice {
    return '${totalPrice.toStringAsFixed(2)} ر.س';
  }

  // Static methods for filtering and sorting
  static List<MedicalProduct> filterByCategory(List<MedicalProduct> products, String category) {
    if (category == 'الكل') return products;
    return products.where((product) => product.category == category).toList();
  }

  static List<MedicalProduct> filterByBrand(List<MedicalProduct> products, String brand) {
    return products.where((product) => product.brand == brand).toList();
  }

  static List<MedicalProduct> filterByPriceRange(List<MedicalProduct> products, double minPrice, double maxPrice) {
    return products.where((product) => 
        product.price >= minPrice && product.price <= maxPrice).toList();
  }

  static List<MedicalProduct> filterByRating(List<MedicalProduct> products, double minRating) {
    return products.where((product) => product.rating >= minRating).toList();
  }

  static List<MedicalProduct> filterByInStock(List<MedicalProduct> products) {
    return products.where((product) => product.isInStock).toList();
  }

  static List<MedicalProduct> filterByFavorites(List<MedicalProduct> products) {
    return products.where((product) => product.isFavorite).toList();
  }

  static List<MedicalProduct> filterByDiscount(List<MedicalProduct> products) {
    return products.where((product) => product.hasDiscount).toList();
  }

  static List<MedicalProduct> sortByPrice(List<MedicalProduct> products, {bool ascending = true}) {
    List<MedicalProduct> sorted = List.from(products);
    sorted.sort((a, b) => ascending 
        ? a.price.compareTo(b.price)
        : b.price.compareTo(a.price));
    return sorted;
  }

  static List<MedicalProduct> sortByRating(List<MedicalProduct> products, {bool ascending = false}) {
    List<MedicalProduct> sorted = List.from(products);
    sorted.sort((a, b) => ascending 
        ? a.rating.compareTo(b.rating)
        : b.rating.compareTo(a.rating));
    return sorted;
  }

  static List<MedicalProduct> sortByName(List<MedicalProduct> products, {bool ascending = true}) {
    List<MedicalProduct> sorted = List.from(products);
    sorted.sort((a, b) => ascending 
        ? a.name.compareTo(b.name)
        : b.name.compareTo(a.name));
    return sorted;
  }

  static List<MedicalProduct> sortByPopularity(List<MedicalProduct> products) {
    List<MedicalProduct> sorted = List.from(products);
    sorted.sort((a, b) => b.reviewCount.compareTo(a.reviewCount));
    return sorted;
  }

  static List<MedicalProduct> searchByKeyword(List<MedicalProduct> products, String keyword) {
    if (keyword.isEmpty) return products;
    final lowerKeyword = keyword.toLowerCase();
    
    return products.where((product) => 
        product.name.toLowerCase().contains(lowerKeyword) ||
        product.description.toLowerCase().contains(lowerKeyword) ||
        product.category.toLowerCase().contains(lowerKeyword) ||
        product.brand.toLowerCase().contains(lowerKeyword) ||
        product.tags.any((tag) => tag.toLowerCase().contains(lowerKeyword))
    ).toList();
  }

  static Map<String, int> getCategoryStats(List<MedicalProduct> products) {
    Map<String, int> stats = {};
    for (var product in products) {
      stats[product.category] = (stats[product.category] ?? 0) + 1;
    }
    return stats;
  }

  static List<String> getAllBrands(List<MedicalProduct> products) {
    Set<String> allBrands = {};
    for (var product in products) {
      allBrands.add(product.brand);
    }
    return allBrands.toList()..sort();
  }

  static List<String> getAllCategories(List<MedicalProduct> products) {
    Set<String> allCategories = {};
    for (var product in products) {
      allCategories.add(product.category);
    }
    return allCategories.toList()..sort();
  }

  static double getAveragePrice(List<MedicalProduct> products) {
    if (products.isEmpty) return 0.0;
    double total = products.fold(0.0, (sum, product) => sum + product.price);
    return total / products.length;
  }

  static double getAverageRating(List<MedicalProduct> products) {
    if (products.isEmpty) return 0.0;
    double total = products.fold(0.0, (sum, product) => sum + product.rating);
    return total / products.length;
  }
}

// Enum for product categories
enum ProductCategory {
  measurementDevices,
  medicalSupplies,
  assistiveTools,
  rehabilitationEquipment,
  supplements,
  personalCare,
  emergencySupplies,
}

extension ProductCategoryExtension on ProductCategory {
  String get arabicName {
    switch (this) {
      case ProductCategory.measurementDevices:
        return 'أجهزة قياس';
      case ProductCategory.medicalSupplies:
        return 'مستلزمات طبية';
      case ProductCategory.assistiveTools:
        return 'أدوات مساعدة';
      case ProductCategory.rehabilitationEquipment:
        return 'معدات تأهيل';
      case ProductCategory.supplements:
        return 'مكملات غذائية';
      case ProductCategory.personalCare:
        return 'عناية شخصية';
      case ProductCategory.emergencySupplies:
        return 'مستلزمات طوارئ';
    }
  }

  String get iconName {
    switch (this) {
      case ProductCategory.measurementDevices:
        return 'measurement';
      case ProductCategory.medicalSupplies:
        return 'medical_supplies';
      case ProductCategory.assistiveTools:
        return 'assistive_tools';
      case ProductCategory.rehabilitationEquipment:
        return 'rehabilitation';
      case ProductCategory.supplements:
        return 'supplements';
      case ProductCategory.personalCare:
        return 'personal_care';
      case ProductCategory.emergencySupplies:
        return 'emergency';
    }
  }
}
