import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../models/medical_tip_model.dart';

class MedicalTipsScreen extends StatefulWidget {
  const MedicalTipsScreen({super.key});

  @override
  State<MedicalTipsScreen> createState() => _MedicalTipsScreenState();
}

class _MedicalTipsScreenState extends State<MedicalTipsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late PageController _pageController;
  
  List<MedicalTip> _tips = [];
  List<MedicalTip> _filteredTips = [];
  String _selectedCategory = 'الكل';
  int _currentPage = 0;
  bool _isLoading = true;
  
  final List<String> _categories = [
    'الكل',
    'صحة عامة',
    'تغذية',
    'رياضة',
    'نوم',
    'صحة نفسية',
    'وقاية',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _pageController = PageController();
    
    _loadTips();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _loadTips() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _tips = _getMockTips();
          _filteredTips = _tips;
          _isLoading = false;
        });
      }
    });
  }

  List<MedicalTip> _getMockTips() {
    return [
      MedicalTip(
        id: '1',
        title: 'أهمية شرب الماء',
        content: '''
شرب الماء ضروري للحفاظ على صحة الجسم. يساعد الماء في:

• تنظيم درجة حرارة الجسم
• نقل المواد الغذائية للخلايا
• إزالة السموم من الجسم
• تحسين وظائف الكلى
• الحفاظ على رطوبة البشرة

**الكمية المطلوبة:**
- 8-10 أكواب يومياً للبالغين
- زيادة الكمية في الطقس الحار
- شرب الماء قبل الشعور بالعطش

**نصائح مهمة:**
- ابدأ يومك بكوب من الماء
- احمل زجاجة ماء معك دائماً
- اشرب الماء قبل وبعد التمرين
        ''',
        category: 'صحة عامة',
        imageUrl: 'assets/images/water_tip.jpg',
        readingTime: 3,
        isBookmarked: false,
        tags: ['ماء', 'صحة', 'ترطيب'],
        author: 'د. أحمد محمد',
        publishedDate: DateTime.now().subtract(const Duration(days: 1)),
      ),
      MedicalTip(
        id: '2',
        title: 'فوائد المشي اليومي',
        content: '''
المشي من أفضل التمارين الرياضية البسيطة والمفيدة:

**الفوائد الجسدية:**
• تقوية عضلات القلب
• تحسين الدورة الدموية
• حرق السعرات الحرارية
• تقوية العظام والمفاصل
• تحسين التوازن والتناسق

**الفوائد النفسية:**
• تقليل التوتر والقلق
• تحسين المزاج
• زيادة الطاقة
• تحسين جودة النوم

**برنامج المشي المقترح:**
- ابدأ بـ 15-20 دقيقة يومياً
- زد المدة تدريجياً إلى 30-45 دقيقة
- امش بوتيرة معتدلة
- اختر أوقات مناسبة (صباحاً أو مساءً)
        ''',
        category: 'رياضة',
        imageUrl: 'assets/images/walking_tip.jpg',
        readingTime: 4,
        isBookmarked: true,
        tags: ['مشي', 'رياضة', 'صحة القلب'],
        author: 'د. فاطمة علي',
        publishedDate: DateTime.now().subtract(const Duration(days: 2)),
      ),
      MedicalTip(
        id: '3',
        title: 'أهمية النوم الصحي',
        content: '''
النوم الجيد أساس الصحة الجسدية والنفسية:

**فوائد النوم الكافي:**
• تجديد خلايا الجسم
• تقوية جهاز المناعة
• تحسين الذاكرة والتركيز
• تنظيم الهرمونات
• تحسين الأداء اليومي

**عدد ساعات النوم المطلوبة:**
- البالغون: 7-9 ساعات
- المراهقون: 8-10 ساعات
- الأطفال: 9-11 ساعة

**نصائح لنوم أفضل:**
- حافظ على مواعيد نوم ثابتة
- تجنب الكافيين قبل النوم بـ 6 ساعات
- اجعل غرفة النوم مظلمة وهادئة
- تجنب الشاشات قبل النوم بساعة
- مارس الاسترخاء قبل النوم
        ''',
        category: 'نوم',
        imageUrl: 'assets/images/sleep_tip.jpg',
        readingTime: 5,
        isBookmarked: false,
        tags: ['نوم', 'راحة', 'صحة نفسية'],
        author: 'د. محمد حسن',
        publishedDate: DateTime.now().subtract(const Duration(days: 3)),
      ),
      MedicalTip(
        id: '4',
        title: 'التغذية المتوازنة',
        content: '''
التغذية السليمة مفتاح الصحة الجيدة:

**المجموعات الغذائية الأساسية:**
• البروتينات: اللحوم، الأسماك، البقوليات
• الكربوهيدرات: الحبوب الكاملة، الخضار
• الدهون الصحية: المكسرات، زيت الزيتون
• الفيتامينات والمعادن: الفواكه والخضار

**نصائح للتغذية الصحية:**
- تناول 5 حصص من الفواكه والخضار يومياً
- اختر الحبوب الكاملة بدلاً من المكررة
- قلل من السكر والملح
- اشرب الماء بدلاً من المشروبات السكرية
- تناول وجبات منتظمة ومتوازنة

**تجنب:**
- الأطعمة المصنعة
- الدهون المتحولة
- الإفراط في تناول الطعام
        ''',
        category: 'تغذية',
        imageUrl: 'assets/images/nutrition_tip.jpg',
        readingTime: 6,
        isBookmarked: true,
        tags: ['تغذية', 'صحة', 'فيتامينات'],
        author: 'د. سارة أحمد',
        publishedDate: DateTime.now().subtract(const Duration(days: 4)),
      ),
      MedicalTip(
        id: '5',
        title: 'إدارة التوتر والضغط النفسي',
        content: '''
التوتر جزء طبيعي من الحياة، لكن إدارته مهمة:

**علامات التوتر:**
• صداع متكرر
• صعوبة في النوم
• تغيرات في الشهية
• تقلبات مزاجية
• تعب مستمر

**طرق إدارة التوتر:**
- تمارين التنفس العميق
- ممارسة الرياضة بانتظام
- تقنيات الاسترخاء والتأمل
- قضاء وقت مع الأصدقاء والعائلة
- ممارسة الهوايات المفضلة

**متى تطلب المساعدة:**
- إذا استمر التوتر لفترة طويلة
- إذا أثر على حياتك اليومية
- إذا شعرت بأعراض الاكتئاب
- إذا لم تستطع التعامل معه بمفردك
        ''',
        category: 'صحة نفسية',
        imageUrl: 'assets/images/stress_tip.jpg',
        readingTime: 5,
        isBookmarked: false,
        tags: ['توتر', 'صحة نفسية', 'استرخاء'],
        author: 'د. عمر خالد',
        publishedDate: DateTime.now().subtract(const Duration(days: 5)),
      ),
    ];
  }

  void _filterTips(String category) {
    setState(() {
      _selectedCategory = category;
      if (category == 'الكل') {
        _filteredTips = _tips;
      } else {
        _filteredTips = _tips.where((tip) => tip.category == category).toList();
      }
      _currentPage = 0;
      _pageController.animateToPage(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.patientPrimary,
        foregroundColor: AppColors.white,
        elevation: 0,
        title: Text(
          'النصائح الطبية',
          style: AppTheme.heading6.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _showBookmarkedTips(),
            icon: const Icon(Icons.bookmark),
            tooltip: 'النصائح المحفوظة',
          ),
          IconButton(
            onPressed: () => _showSearchDialog(),
            icon: const Icon(Icons.search),
            tooltip: 'البحث',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Categories Filter
            Container(
              height: 60,
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  final isSelected = category == _selectedCategory;
                  
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(category),
                      selected: isSelected,
                      onSelected: (_) => _filterTips(category),
                      backgroundColor: AppColors.surfaceLight,
                      selectedColor: AppColors.patientPrimary.withValues(alpha: 0.2),
                      checkmarkColor: AppColors.patientPrimary,
                      labelStyle: AppTheme.bodySmall.copyWith(
                        color: isSelected 
                            ? AppColors.patientPrimary 
                            : AppColors.textSecondary,
                        fontWeight: isSelected 
                            ? FontWeight.bold 
                            : FontWeight.normal,
                      ),
                      side: BorderSide(
                        color: isSelected 
                            ? AppColors.patientPrimary 
                            : AppColors.borderLight,
                        width: 1,
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Tips Slider
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.patientPrimary,
                      ),
                    )
                  : _filteredTips.isEmpty
                      ? _buildEmptyState()
                      : Column(
                          children: [
                            // Page Indicator
                            if (_filteredTips.length > 1)
                              Container(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: List.generate(
                                    _filteredTips.length,
                                    (index) => Container(
                                      margin: const EdgeInsets.symmetric(horizontal: 4),
                                      width: index == _currentPage ? 24 : 8,
                                      height: 8,
                                      decoration: BoxDecoration(
                                        color: index == _currentPage
                                            ? AppColors.patientPrimary
                                            : AppColors.borderLight,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            
                            // Tips PageView
                            Expanded(
                              child: PageView.builder(
                                controller: _pageController,
                                onPageChanged: (index) {
                                  setState(() {
                                    _currentPage = index;
                                  });
                                },
                                itemCount: _filteredTips.length,
                                itemBuilder: (context, index) {
                                  return _buildTipCard(_filteredTips[index]);
                                },
                              ),
                            ),
                          ],
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipCard(MedicalTip tip) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Image
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.patientPrimary.withValues(alpha: 0.8),
                  AppColors.patientPrimary.withValues(alpha: 0.6),
                ],
              ),
            ),
            child: Stack(
              children: [
                // Background pattern
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      image: DecorationImage(
                        image: AssetImage(tip.imageUrl),
                        fit: BoxFit.cover,
                        opacity: 0.3,
                        onError: (exception, stackTrace) {
                          // Handle missing image gracefully
                        },
                      ),
                    ),
                  ),
                ),
                
                // Content overlay
                Positioned.fill(
                  child: Container(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Category and bookmark
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.white.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                tip.category,
                                style: AppTheme.caption.copyWith(
                                  color: AppColors.patientPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const Spacer(),
                            GestureDetector(
                              onTap: () => _toggleBookmark(tip),
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.white.withValues(alpha: 0.9),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  tip.isBookmarked 
                                      ? Icons.bookmark 
                                      : Icons.bookmark_border,
                                  color: tip.isBookmarked 
                                      ? AppColors.warning 
                                      : AppColors.textSecondary,
                                  size: 20,
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const Spacer(),
                        
                        // Title
                        Text(
                          tip.title,
                          style: AppTheme.heading4.copyWith(
                            color: AppColors.white,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // Meta info
                        Row(
                          children: [
                            Icon(
                              Icons.schedule,
                              color: AppColors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${tip.readingTime} دقائق قراءة',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppColors.white,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Icon(
                              Icons.person,
                              color: AppColors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                tip.author,
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppColors.white,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tags
                  if (tip.tags.isNotEmpty)
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: tip.tags.map((tag) {
                        return Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.patientPrimary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: AppColors.patientPrimary.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            '#$tag',
                            style: AppTheme.caption.copyWith(
                              color: AppColors.patientPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // Content preview
                  Expanded(
                    child: SingleChildScrollView(
                      child: Text(
                        tip.content,
                        style: AppTheme.bodyMedium.copyWith(
                          color: AppColors.textPrimary,
                          height: 1.6,
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Action button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _readFullTip(tip),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.patientPrimary,
                        foregroundColor: AppColors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'قراءة كاملة',
                        style: AppTheme.buttonMedium.copyWith(
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد نصائح متاحة',
            style: AppTheme.heading6.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفئة أو البحث عن نصائح أخرى',
            style: AppTheme.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _toggleBookmark(MedicalTip tip) {
    setState(() {
      tip.isBookmarked = !tip.isBookmarked;
    });
    HapticFeedback.lightImpact();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          tip.isBookmarked 
              ? 'تم حفظ النصيحة' 
              : 'تم إلغاء حفظ النصيحة',
        ),
        backgroundColor: tip.isBookmarked 
            ? AppColors.success 
            : AppColors.textSecondary,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _readFullTip(MedicalTip tip) {
    HapticFeedback.lightImpact();
    // Navigate to full tip screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('شاشة القراءة الكاملة ستكون متاحة قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showBookmarkedTips() {
    final bookmarkedTips = _tips.where((tip) => tip.isBookmarked).toList();
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.borderLight,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(
                    Icons.bookmark,
                    color: AppColors.warning,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'النصائح المحفوظة',
                    style: AppTheme.heading5.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            // Bookmarked tips list
            Expanded(
              child: bookmarkedTips.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.bookmark_border,
                            size: 48,
                            color: AppColors.textSecondary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد نصائح محفوظة',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: bookmarkedTips.length,
                      itemBuilder: (context, index) {
                        final tip = bookmarkedTips[index];
                        return Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.surfaceLight,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: AppColors.borderLight,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.patientPrimary.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  Icons.lightbulb,
                                  color: AppColors.patientPrimary,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      tip.title,
                                      style: AppTheme.bodyMedium.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: AppColors.textPrimary,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      tip.category,
                                      style: AppTheme.bodySmall.copyWith(
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              IconButton(
                                onPressed: () => _toggleBookmark(tip),
                                icon: const Icon(
                                  Icons.bookmark,
                                  color: AppColors.warning,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في النصائح'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'اكتب كلمة البحث...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            // Implement search functionality
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }
}
