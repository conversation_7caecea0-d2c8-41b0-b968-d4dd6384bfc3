# Arabic Fonts Setup Guide

This guide will help you download and install the required Arabic fonts for the Khatwa app.

## Required Fonts

### 1. Cairo Font
- **Primary font for the app**
- Download from: https://fonts.google.com/specimen/Cairo
- Files needed:
  - `Cairo-Regular.ttf`
  - `Cairo-Bold.ttf`
  - `Cairo-Light.ttf`

### 2. <PERSON><PERSON>wal Font
- **Secondary font for the app**
- Download from: https://fonts.google.com/specimen/Tajawal
- Files needed:
  - `Ta<PERSON>wal-Regular.ttf`
  - `Tajawal-Bold.ttf`
  - `Ta<PERSON>wal-Light.ttf`

### 3. <PERSON><PERSON>ont
- **Decorative font for headings**
- Download from: https://fonts.google.com/specimen/Amiri
- Files needed:
  - `Amiri-Regular.ttf`
  - `Amiri-Bold.ttf`

## Installation Steps

### Step 1: Download Fonts
1. Visit each font's Google Fonts page
2. Click "Download family"
3. Extract the ZIP files
4. Locate the TTF files

### Step 2: Install in Project
1. Copy the TTF files to `assets/fonts/` directory
2. Make sure the file names match exactly:
   ```
   assets/fonts/
   ├── Cairo-Regular.ttf
   ├── Cairo-Bold.ttf
   ├── Cairo-Light.ttf
   ├── Tajawal-Regular.ttf
   ├── Tajawal-Bold.ttf
   ├── Tajawal-Light.ttf
   ├── Amiri-Regular.ttf
   └── Amiri-Bold.ttf
   ```

### Step 3: Verify Configuration
The fonts are already configured in `pubspec.yaml`. After adding the font files, run:
```bash
flutter pub get
flutter clean
flutter run
```

## Alternative: Use System Fonts
If you can't download the fonts, the app will fallback to system fonts:
- Roboto (Android)
- San Francisco (iOS)
- Arial (Web)

## Font Usage in App
- **Cairo**: Main UI text, buttons, labels
- **Tajawal**: Secondary text, descriptions
- **Amiri**: Decorative headings, app name

## Troubleshooting

### Fonts not showing
1. Check file names match exactly
2. Ensure files are in `assets/fonts/` directory
3. Run `flutter clean` and `flutter pub get`
4. Restart the app

### File size concerns
- Each font file is approximately 200-500KB
- Total font size: ~2-3MB
- Consider using only Regular and Bold weights if size is critical

## License
All fonts are available under the Open Font License (OFL), making them free for commercial use.
