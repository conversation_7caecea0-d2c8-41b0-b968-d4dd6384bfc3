import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../widgets/common/gradient_button.dart';

class RegistrationSelectionScreen extends StatelessWidget {
  const RegistrationSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final String? preferredRole =
        ModalRoute.of(context)?.settings.arguments as String?;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Color(0xFF6B73FF), Color(0xFF9B59B6)],
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: <PERSON>umn(
              children: [
                const SizedBox(height: 40),

                // Back Button
                Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: AppColors.white,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Logo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.person_add,
                    size: 60,
                    color: Color(0xFF6B73FF),
                  ),
                ),

                const SizedBox(height: 32),

                // Title
                Text(
                  'إنشاء حساب جديد',
                  style: AppTheme.heading3.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                // Subtitle
                Text(
                  'اختر نوع الحساب الذي تريد إنشاؤه',
                  style: AppTheme.bodyLarge.copyWith(
                    color: AppColors.white.withOpacity(0.9),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 60),

                // Registration Options
                Expanded(
                  child: Column(
                    children: [
                      // Patient Registration
                      _buildRegistrationCard(
                        context: context,
                        title: 'مريض',
                        subtitle: 'أحتاج إلى رعاية طبية واستشارات',
                        icon: Icons.person,
                        gradient: AppColors.patientGradient,
                        isRecommended: preferredRole == 'patient',
                        onTap: () =>
                            _navigateToRegistration(context, 'patient'),
                      ),

                      const SizedBox(height: 20),

                      // Doctor Registration
                      _buildRegistrationCard(
                        context: context,
                        title: 'طبيب / مختص',
                        subtitle: 'أقدم الخدمات الطبية والاستشارات',
                        icon: Icons.medical_services,
                        gradient: AppColors.doctorGradient,
                        isRecommended: preferredRole == 'doctor',
                        onTap: () => _navigateToRegistration(context, 'doctor'),
                      ),

                      const SizedBox(height: 40),

                      // Info Note
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppColors.white.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: AppColors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'حسابات الإدارة محجوزة للمسؤولين المعتمدين فقط',
                                style: AppTheme.bodySmall.copyWith(
                                  color: AppColors.white.withOpacity(0.9),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Back to Login Link
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'لديك حساب بالفعل؟ ',
                            style: AppTheme.bodyMedium.copyWith(
                              color: AppColors.white.withOpacity(0.8),
                            ),
                          ),
                          GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Text(
                              'تسجيل الدخول',
                              style: AppTheme.labelLarge.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRegistrationCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Gradient gradient,
    required bool isRecommended,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(20),
          border: isRecommended
              ? Border.all(color: AppColors.white, width: 2)
              : null,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            if (isRecommended)
              Container(
                margin: const EdgeInsets.only(bottom: 12),
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'مُوصى به',
                  style: AppTheme.labelSmall.copyWith(
                    color: const Color(0xFF6B73FF),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppColors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, size: 30, color: AppColors.white),
            ),

            const SizedBox(height: 16),

            Text(
              title,
              style: AppTheme.heading5.copyWith(
                color: AppColors.white,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              subtitle,
              style: AppTheme.bodyMedium.copyWith(
                color: AppColors.white.withOpacity(0.9),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToRegistration(BuildContext context, String userType) {
    // For now, show coming soon message
    // In the future, these will navigate to actual registration screens
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.info_outline, color: AppColors.info),
            const SizedBox(width: 8),
            const Text('قريباً'),
          ],
        ),
        content: Text(
          userType == 'patient'
              ? 'تسجيل المرضى سيكون متاحاً قريباً'
              : 'تسجيل الأطباء سيكون متاحاً قريباً',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pop(context); // Go back to login
            },
            child: const Text('العودة لتسجيل الدخول'),
          ),
        ],
      ),
    );
  }
}
