class MedicalTip {
  final String id;
  final String title;
  final String content;
  final String category;
  final String imageUrl;
  final int readingTime; // in minutes
  bool isBookmarked;
  final List<String> tags;
  final String author;
  final DateTime publishedDate;
  final int views;
  final double rating;
  final List<String> relatedTips;
  final String? videoUrl;
  final Map<String, dynamic>? metadata;

  MedicalTip({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.imageUrl,
    required this.readingTime,
    this.isBookmarked = false,
    this.tags = const [],
    required this.author,
    required this.publishedDate,
    this.views = 0,
    this.rating = 0.0,
    this.relatedTips = const [],
    this.videoUrl,
    this.metadata,
  });

  factory MedicalTip.fromJson(Map<String, dynamic> json) {
    return MedicalTip(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      category: json['category'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      readingTime: json['readingTime'] ?? 0,
      isBookmarked: json['isBookmarked'] ?? false,
      tags: List<String>.from(json['tags'] ?? []),
      author: json['author'] ?? '',
      publishedDate: DateTime.parse(json['publishedDate'] ?? DateTime.now().toIso8601String()),
      views: json['views'] ?? 0,
      rating: (json['rating'] ?? 0.0).toDouble(),
      relatedTips: List<String>.from(json['relatedTips'] ?? []),
      videoUrl: json['videoUrl'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'category': category,
      'imageUrl': imageUrl,
      'readingTime': readingTime,
      'isBookmarked': isBookmarked,
      'tags': tags,
      'author': author,
      'publishedDate': publishedDate.toIso8601String(),
      'views': views,
      'rating': rating,
      'relatedTips': relatedTips,
      'videoUrl': videoUrl,
      'metadata': metadata,
    };
  }

  MedicalTip copyWith({
    String? id,
    String? title,
    String? content,
    String? category,
    String? imageUrl,
    int? readingTime,
    bool? isBookmarked,
    List<String>? tags,
    String? author,
    DateTime? publishedDate,
    int? views,
    double? rating,
    List<String>? relatedTips,
    String? videoUrl,
    Map<String, dynamic>? metadata,
  }) {
    return MedicalTip(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      category: category ?? this.category,
      imageUrl: imageUrl ?? this.imageUrl,
      readingTime: readingTime ?? this.readingTime,
      isBookmarked: isBookmarked ?? this.isBookmarked,
      tags: tags ?? this.tags,
      author: author ?? this.author,
      publishedDate: publishedDate ?? this.publishedDate,
      views: views ?? this.views,
      rating: rating ?? this.rating,
      relatedTips: relatedTips ?? this.relatedTips,
      videoUrl: videoUrl ?? this.videoUrl,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MedicalTip && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MedicalTip(id: $id, title: $title, category: $category, author: $author)';
  }

  // Helper methods
  String get formattedPublishDate {
    final now = DateTime.now();
    final difference = now.difference(publishedDate);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }

  String get readingTimeText {
    if (readingTime == 1) {
      return 'دقيقة واحدة';
    } else if (readingTime == 2) {
      return 'دقيقتان';
    } else if (readingTime <= 10) {
      return '$readingTime دقائق';
    } else {
      return '$readingTime دقيقة';
    }
  }

  String get formattedViews {
    if (views < 1000) {
      return views.toString();
    } else if (views < 1000000) {
      return '${(views / 1000).toStringAsFixed(1)}ك';
    } else {
      return '${(views / 1000000).toStringAsFixed(1)}م';
    }
  }

  String get ratingText {
    return rating.toStringAsFixed(1);
  }

  bool get hasVideo => videoUrl != null && videoUrl!.isNotEmpty;

  bool get isPopular => views > 1000;

  bool get isHighRated => rating >= 4.0;

  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(publishedDate);
    return difference.inDays <= 7;
  }

  List<String> get contentPreview {
    // Split content into paragraphs and return first few
    final paragraphs = content.split('\n\n');
    return paragraphs.take(2).toList();
  }

  String get shortDescription {
    // Get first paragraph or first 150 characters
    final firstParagraph = content.split('\n\n').first;
    if (firstParagraph.length <= 150) {
      return firstParagraph;
    }
    return '${firstParagraph.substring(0, 147)}...';
  }

  // Static methods for filtering and sorting
  static List<MedicalTip> filterByCategory(List<MedicalTip> tips, String category) {
    if (category == 'الكل') return tips;
    return tips.where((tip) => tip.category == category).toList();
  }

  static List<MedicalTip> filterByBookmarked(List<MedicalTip> tips) {
    return tips.where((tip) => tip.isBookmarked).toList();
  }

  static List<MedicalTip> filterByTag(List<MedicalTip> tips, String tag) {
    return tips.where((tip) => tip.tags.contains(tag)).toList();
  }

  static List<MedicalTip> filterByAuthor(List<MedicalTip> tips, String author) {
    return tips.where((tip) => tip.author == author).toList();
  }

  static List<MedicalTip> filterByRecent(List<MedicalTip> tips, {int days = 7}) {
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    return tips.where((tip) => tip.publishedDate.isAfter(cutoffDate)).toList();
  }

  static List<MedicalTip> filterByPopular(List<MedicalTip> tips, {int minViews = 1000}) {
    return tips.where((tip) => tip.views >= minViews).toList();
  }

  static List<MedicalTip> sortByDate(List<MedicalTip> tips, {bool ascending = false}) {
    List<MedicalTip> sorted = List.from(tips);
    sorted.sort((a, b) => ascending 
        ? a.publishedDate.compareTo(b.publishedDate)
        : b.publishedDate.compareTo(a.publishedDate));
    return sorted;
  }

  static List<MedicalTip> sortByViews(List<MedicalTip> tips, {bool ascending = false}) {
    List<MedicalTip> sorted = List.from(tips);
    sorted.sort((a, b) => ascending 
        ? a.views.compareTo(b.views)
        : b.views.compareTo(a.views));
    return sorted;
  }

  static List<MedicalTip> sortByRating(List<MedicalTip> tips, {bool ascending = false}) {
    List<MedicalTip> sorted = List.from(tips);
    sorted.sort((a, b) => ascending 
        ? a.rating.compareTo(b.rating)
        : b.rating.compareTo(a.rating));
    return sorted;
  }

  static List<MedicalTip> sortByTitle(List<MedicalTip> tips, {bool ascending = true}) {
    List<MedicalTip> sorted = List.from(tips);
    sorted.sort((a, b) => ascending 
        ? a.title.compareTo(b.title)
        : b.title.compareTo(a.title));
    return sorted;
  }

  static List<MedicalTip> searchByKeyword(List<MedicalTip> tips, String keyword) {
    if (keyword.isEmpty) return tips;
    final lowerKeyword = keyword.toLowerCase();
    
    return tips.where((tip) => 
        tip.title.toLowerCase().contains(lowerKeyword) ||
        tip.content.toLowerCase().contains(lowerKeyword) ||
        tip.category.toLowerCase().contains(lowerKeyword) ||
        tip.author.toLowerCase().contains(lowerKeyword) ||
        tip.tags.any((tag) => tag.toLowerCase().contains(lowerKeyword))
    ).toList();
  }

  static Map<String, int> getCategoryStats(List<MedicalTip> tips) {
    Map<String, int> stats = {};
    for (var tip in tips) {
      stats[tip.category] = (stats[tip.category] ?? 0) + 1;
    }
    return stats;
  }

  static List<String> getAllTags(List<MedicalTip> tips) {
    Set<String> allTags = {};
    for (var tip in tips) {
      allTags.addAll(tip.tags);
    }
    return allTags.toList()..sort();
  }

  static List<String> getAllAuthors(List<MedicalTip> tips) {
    Set<String> allAuthors = {};
    for (var tip in tips) {
      allAuthors.add(tip.author);
    }
    return allAuthors.toList()..sort();
  }

  static List<String> getAllCategories(List<MedicalTip> tips) {
    Set<String> allCategories = {};
    for (var tip in tips) {
      allCategories.add(tip.category);
    }
    return allCategories.toList()..sort();
  }
}

// Enum for tip categories
enum TipCategory {
  general,
  nutrition,
  exercise,
  sleep,
  mentalHealth,
  prevention,
  medication,
  emergency,
}

extension TipCategoryExtension on TipCategory {
  String get arabicName {
    switch (this) {
      case TipCategory.general:
        return 'صحة عامة';
      case TipCategory.nutrition:
        return 'تغذية';
      case TipCategory.exercise:
        return 'رياضة';
      case TipCategory.sleep:
        return 'نوم';
      case TipCategory.mentalHealth:
        return 'صحة نفسية';
      case TipCategory.prevention:
        return 'وقاية';
      case TipCategory.medication:
        return 'أدوية';
      case TipCategory.emergency:
        return 'طوارئ';
    }
  }

  String get iconName {
    switch (this) {
      case TipCategory.general:
        return 'health';
      case TipCategory.nutrition:
        return 'nutrition';
      case TipCategory.exercise:
        return 'exercise';
      case TipCategory.sleep:
        return 'sleep';
      case TipCategory.mentalHealth:
        return 'mental_health';
      case TipCategory.prevention:
        return 'prevention';
      case TipCategory.medication:
        return 'medication';
      case TipCategory.emergency:
        return 'emergency';
    }
  }
}
