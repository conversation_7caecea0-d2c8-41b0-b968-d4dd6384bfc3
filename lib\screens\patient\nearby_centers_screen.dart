import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_theme.dart';
import '../../models/medical_center_model.dart';

class NearbyCentersScreen extends StatefulWidget {
  const NearbyCentersScreen({super.key});

  @override
  State<NearbyCentersScreen> createState() => _NearbyCentersScreenState();
}

class _NearbyCentersScreenState extends State<NearbyCentersScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  List<MedicalCenter> _centers = [];
  List<MedicalCenter> _filteredCenters = [];
  String _selectedFilter = 'الكل';
  bool _isLoading = true;

  final List<String> _filterOptions = [
    'الكل',
    'مستشفيات',
    'عيادات',
    'مراكز تأهيل',
    'صيدليات',
    'مختبرات',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _loadCenters();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadCenters() {
    // Mock data - في التطبيق الحقيقي سيتم جلب البيانات من API
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _centers = _getMockCenters();
          _filteredCenters = _centers;
          _isLoading = false;
        });
      }
    });
  }

  List<MedicalCenter> _getMockCenters() {
    return [
      MedicalCenter(
        name: 'مستشفى الملك فهد التخصصي',
        type: CenterType.hospital,
        services: [
          ServiceType.generalMedicine,
          ServiceType.surgery,
          ServiceType.diagnostics,
        ],
        location: Location(
          address: 'شارع الملك فهد، الرياض',
          city: 'الرياض',
          state: 'الرياض',
          country: 'السعودية',
          latitude: 24.7136,
          longitude: 46.6753,
        ),
        contactInfo: ContactInfo(
          phone: '+966112345678',
          email: '<EMAIL>',
        ),
        rating: 4.8,
        workingHours: [
          WorkingHours(day: 'الأحد', openTime: '00:00', closeTime: '23:59'),
        ],
        imageUrls: ['assets/images/hospital1.jpg'],
      ),
      MedicalCenter(
        name: 'عيادات الدكتور أحمد',
        type: CenterType.clinic,
        services: [ServiceType.generalMedicine, ServiceType.diagnostics],
        location: Location(
          address: 'حي النخيل، الرياض',
          city: 'الرياض',
          state: 'الرياض',
          country: 'السعودية',
          latitude: 24.7236,
          longitude: 46.6853,
        ),
        contactInfo: ContactInfo(
          phone: '+966112345679',
          email: '<EMAIL>',
        ),
        rating: 4.5,
        workingHours: [
          WorkingHours(day: 'الأحد', openTime: '08:00', closeTime: '22:00'),
        ],
        imageUrls: ['assets/images/clinic1.jpg'],
      ),
      MedicalCenter(
        name: 'مركز التأهيل الطبي المتقدم',
        type: CenterType.rehabilitationCenter,
        services: [
          ServiceType.physiotherapy,
          ServiceType.occupationalTherapy,
          ServiceType.rehabilitation,
        ],
        location: Location(
          address: 'شارع العليا، الرياض',
          city: 'الرياض',
          state: 'الرياض',
          country: 'السعودية',
          latitude: 24.7036,
          longitude: 46.6653,
        ),
        contactInfo: ContactInfo(
          phone: '+966112345680',
          email: '<EMAIL>',
        ),
        rating: 4.7,
        workingHours: [
          WorkingHours(day: 'الأحد', openTime: '07:00', closeTime: '19:00'),
        ],
        imageUrls: ['assets/images/rehab1.jpg'],
      ),
    ];
  }

  void _filterCenters(String filter) {
    setState(() {
      _selectedFilter = filter;
      if (filter == 'الكل') {
        _filteredCenters = _centers;
      } else {
        _filteredCenters = _centers.where((center) {
          switch (filter) {
            case 'مستشفيات':
              return center.type == CenterType.hospital;
            case 'عيادات':
              return center.type == CenterType.clinic;
            case 'مراكز تأهيل':
              return center.type == CenterType.rehabilitationCenter;
            case 'صيدليات':
              return center.type ==
                  CenterType.prostheticsCenter; // أقرب نوع متاح
            case 'مختبرات':
              return center.type == CenterType.specializedCenter;
            default:
              return true;
          }
        }).toList();
      }
    });
  }

  // Helper methods
  String _getCenterTypeText(CenterType type) {
    switch (type) {
      case CenterType.hospital:
        return 'مستشفى';
      case CenterType.clinic:
        return 'عيادة';
      case CenterType.rehabilitationCenter:
        return 'مركز تأهيل';
      case CenterType.prostheticsCenter:
        return 'مركز أطراف صناعية';
      case CenterType.physiotherapyCenter:
        return 'مركز علاج طبيعي';
      case CenterType.specializedCenter:
        return 'مركز متخصص';
    }
  }

  bool _isCenterOpen(MedicalCenter center) {
    // Mock logic - في التطبيق الحقيقي سيتم فحص الوقت الحالي مع ساعات العمل
    return center.workingHours.isNotEmpty;
  }

  double _getCenterDistance(MedicalCenter center) {
    // Mock distance calculation - في التطبيق الحقيقي سيتم حساب المسافة الفعلية
    return 1.5; // كم
  }

  String _getServiceText(ServiceType service) {
    switch (service) {
      case ServiceType.physiotherapy:
        return 'علاج طبيعي';
      case ServiceType.prosthetics:
        return 'أطراف صناعية';
      case ServiceType.orthopedics:
        return 'عظام';
      case ServiceType.neurology:
        return 'أعصاب';
      case ServiceType.rehabilitation:
        return 'تأهيل';
      case ServiceType.occupationalTherapy:
        return 'علاج وظيفي';
      case ServiceType.psychology:
        return 'نفسي';
      case ServiceType.generalMedicine:
        return 'طب عام';
      case ServiceType.surgery:
        return 'جراحة';
      case ServiceType.diagnostics:
        return 'تشخيص';
      case ServiceType.other:
        return 'أخرى';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.patientPrimary,
        foregroundColor: AppColors.white,
        elevation: 0,
        title: Text(
          'المراكز القريبة',
          style: AppTheme.heading6.copyWith(
            color: AppColors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _showMapView(),
            icon: const Icon(Icons.map),
            tooltip: 'عرض الخريطة',
          ),
          IconButton(
            onPressed: () => _showSearchDialog(),
            icon: const Icon(Icons.search),
            tooltip: 'البحث',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Filter Chips
            Container(
              height: 60,
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _filterOptions.length,
                itemBuilder: (context, index) {
                  final filter = _filterOptions[index];
                  final isSelected = filter == _selectedFilter;

                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: FilterChip(
                      label: Text(filter),
                      selected: isSelected,
                      onSelected: (_) => _filterCenters(filter),
                      backgroundColor: AppColors.surfaceLight,
                      selectedColor: AppColors.patientPrimary.withValues(
                        alpha: 0.2,
                      ),
                      checkmarkColor: AppColors.patientPrimary,
                      labelStyle: AppTheme.bodySmall.copyWith(
                        color: isSelected
                            ? AppColors.patientPrimary
                            : AppColors.textSecondary,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                      side: BorderSide(
                        color: isSelected
                            ? AppColors.patientPrimary
                            : AppColors.borderLight,
                        width: 1,
                      ),
                    ),
                  );
                },
              ),
            ),

            // Centers List
            Expanded(
              child: _isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        color: AppColors.patientPrimary,
                      ),
                    )
                  : _filteredCenters.isEmpty
                  ? _buildEmptyState()
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: _filteredCenters.length,
                      itemBuilder: (context, index) {
                        return _buildCenterCard(_filteredCenters[index]);
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showMapView(),
        backgroundColor: AppColors.patientPrimary,
        foregroundColor: AppColors.white,
        icon: const Icon(Icons.map),
        label: const Text('عرض الخريطة'),
      ),
    );
  }

  Widget _buildCenterCard(MedicalCenter center) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with image and basic info
          Container(
            height: 120,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.patientPrimary.withValues(alpha: 0.8),
                  AppColors.patientPrimary.withValues(alpha: 0.6),
                ],
              ),
            ),
            child: Stack(
              children: [
                // Background pattern
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                      image: center.imageUrls.isNotEmpty
                          ? DecorationImage(
                              image: AssetImage(center.imageUrls.first),
                              fit: BoxFit.cover,
                              opacity: 0.3,
                              onError: (exception, stackTrace) {
                                // Handle missing image gracefully
                              },
                            )
                          : null,
                    ),
                  ),
                ),

                // Content
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.white.withValues(alpha: 0.9),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _getCenterTypeText(center.type),
                              style: AppTheme.caption.copyWith(
                                color: AppColors.patientPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          const Spacer(),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _isCenterOpen(center)
                                  ? AppColors.success.withValues(alpha: 0.9)
                                  : AppColors.error.withValues(alpha: 0.9),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              _isCenterOpen(center) ? 'مفتوح' : 'مغلق',
                              style: AppTheme.caption.copyWith(
                                color: AppColors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Text(
                        center.name,
                        style: AppTheme.heading6.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            color: AppColors.white,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${_getCenterDistance(center)} كم',
                            style: AppTheme.bodySmall.copyWith(
                              color: AppColors.white,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Icon(Icons.star, color: AppColors.warning, size: 16),
                          const SizedBox(width: 4),
                          Text(
                            center.rating.toString(),
                            style: AppTheme.bodySmall.copyWith(
                              color: AppColors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Address and phone
                Row(
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      color: AppColors.textSecondary,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        center.location.address,
                        style: AppTheme.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      color: AppColors.textSecondary,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      center.workingHours.isNotEmpty
                          ? '${center.workingHours.first.openTime} - ${center.workingHours.first.closeTime}'
                          : 'غير محدد',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Services
                Text(
                  'الخدمات المتاحة:',
                  style: AppTheme.bodyMedium.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: center.services.map((service) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.patientPrimary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppColors.patientPrimary.withValues(
                            alpha: 0.3,
                          ),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        _getServiceText(service),
                        style: AppTheme.caption.copyWith(
                          color: AppColors.patientPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),

                const SizedBox(height: 16),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _callCenter(center),
                        icon: const Icon(Icons.phone, size: 18),
                        label: const Text('اتصال'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.patientPrimary,
                          side: BorderSide(color: AppColors.patientPrimary),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _getDirections(center),
                        icon: const Icon(Icons.directions, size: 18),
                        label: const Text('الاتجاهات'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.patientPrimary,
                          foregroundColor: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.location_off, size: 64, color: AppColors.textSecondary),
          const SizedBox(height: 16),
          Text(
            'لا توجد مراكز متاحة',
            style: AppTheme.heading6.copyWith(color: AppColors.textSecondary),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفلتر أو البحث في منطقة أخرى',
            style: AppTheme.bodyMedium.copyWith(color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showMapView() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('عرض الخريطة سيكون متاحاً قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث عن مركز'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'اكتب اسم المركز أو الخدمة...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (value) {
            // Implement search functionality
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  void _callCenter(MedicalCenter center) {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('الاتصال بـ ${center.name}: ${center.contactInfo.phone}'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _getDirections(MedicalCenter center) {
    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('فتح الاتجاهات إلى ${center.name}'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
